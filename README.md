# ChatRot(纯前端版)

#### 介绍

基于react的chatgpt聊天机器人，页面使用了蚂蚁的chatUI组件库，简单易上手，让你十分钟搭建属于自己的chatgpt问答机器人！

#### 使用说明

1.  本项目使用GPT-3.5-turbo,支持记录上下文实现连续对话！

2.  首次使用需要设置自己的 openAI 的api key,api key保存本地,不会有安全问题！

3.  项目纯前端页面,使用gitee pages或者 github pages部署就行！

4.  支持切换API Server,使用网络代理后不需要科学上网！

5.  有独立的APP,目前只支持安卓系统安装！

6.  新增"奇思妙想",目前支持知乎热榜！

### `npm install`

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.
